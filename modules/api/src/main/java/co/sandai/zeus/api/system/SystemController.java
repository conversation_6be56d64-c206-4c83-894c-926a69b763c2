package co.sandai.zeus.api.system;

import co.sandai.config.SystemConfig;
import co.sandai.zeus.api.system.view.SystemConfigView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class SystemController {

    @Autowired
    private SystemConfig systemConfig;

    @GetMapping("/api/v1/system/config")
    public SystemConfigView systemConfig() {
        return SystemConfigView.builder()
                .disableEmailLogin(systemConfig.isDisableEmailLogin())
                .build();
    }
}
